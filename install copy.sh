#!/usr/bin/env bash

# 脚本用途：安装Docker及其相关组件
# 使用说明：./install.sh [配置文件路径]
# 参数说明：
#   - 配置文件路径：YAML格式的配置文件，包含安装参数

set -o errexit  # 发生错误立即退出
set -o nounset  # 使用未定义的变量时报错
set -o pipefail # 管道中的任何命令失败都会导致整个管道失败

# 清除不需要的环境变量
unset CDPATH

# 常量定义
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly DOCKER_BIN_DIR="/usr/bin"
readonly DOCKER_SERVICE_DIR="/etc/systemd/system"
readonly DOCKER_CONFIG_DIR="/etc/docker"
readonly DEFAULT_CONFIG_FILE="${SCRIPT_DIR}/config.yaml"

# 配置变量
CONFIG_FILE=""
STORAGE_DEVICE=""

# 日志函数
log_info() { echo "INFO: $*" >&2; }
log_error() { echo "ERROR: $*" >&2; }
log_warn() { echo "WARN: $*" >&2; }

# 显示使用说明
show_usage() {
  cat << EOF
使用方法: $0 [选项] [配置文件路径]

选项:
  -h, --help     显示此帮助信息

}

# 解析命令行参数
parse_args() {
  while [[ $# -gt 0 ]]; do
    case $1 in
      -h|--help)
        show_usage
        exit 0
        ;;
      -*)
        log_error "未知选项: $1"
        show_usage
        exit 1
        ;;

    esac
  done

}



# 创建 LVM 并格式化逻辑卷
setup_lvm_on_node() {
    local ip=$1
    local disk=$2

    log "开始设置节点 $ip (磁盘: $disk)"

    # 检查磁盘是否存在
    if ! run_on_node "$ip" "test -b $disk"; then
        log "❌ 磁盘 $disk 不存在或不是一个块设备"
        return 1
    fi
    # ==== 添加清理操作 ====
    log "正在清理 $ip 上可能存在的 LVM 数据..."
    # 判断是否有 VG 存在
    if run_on_node "$ip" "vgdisplay $VG_NAME &>/dev/null"; then
      run_on_node "$ip" "umount $MOUNT_DIR1 $MOUNT_DIR2 $MOUNT_DIR3"
      run_on_node "$ip" "lvremove -f /dev/mapper/${VG_NAME}-*"
      run_on_node "$ip" "vgremove -f $VG_NAME"
      run_on_node "$ip" "pvremove -f $disk"
      run_on_node "$ip" "rm -rf $MOUNT_DIR1/* $MOUNT_DIR2/* $MOUNT_DIR3/*"
    fi

    # ==== 开始创建 LVM ====
    # 1. 创建物理卷
    run_on_node "$ip" "pvcreate -f $disk" || return 1

    # 2. 创建卷组
    run_on_node "$ip" "vgcreate $VG_NAME $disk" || return 1

    # 3. 创建逻辑卷（根据定义的空间比例）
    run_on_node "$ip" "lvcreate --yes -l $LV_MNT_SIZE -n $LV_MNT $VG_NAME" || return 1
    run_on_node "$ip" "lvcreate --yes -l $LV_KUBE_SIZE -n $LV_KUBE $VG_NAME" || return 1
    run_on_node "$ip" "lvcreate --yes -l $LV_OPENEBS_SIZE -n $LV_OPENEBS $VG_NAME" || return 1

    # 4. 格式化为 XFS
    run_on_node "$ip" "mkfs.xfs -f /dev/mapper/${VG_NAME}-${LV_MNT}" || return 1
    run_on_node "$ip" "mkfs.xfs -f /dev/mapper/${VG_NAME}-${LV_KUBE}" || return 1
    run_on_node "$ip" "mkfs.xfs -f /dev/mapper/${VG_NAME}-${LV_OPENEBS}" || return 1

    # 5. 创建挂载点（如果不存在）
    run_on_node "$ip" "mkdir -p $MOUNT_DIR1 $MOUNT_DIR2 $MOUNT_DIR3" || return 1

    # 6. 挂载
    run_on_node "$ip" "mount /dev/mapper/${VG_NAME}-${LV_MNT} $MOUNT_DIR1" || return 1
    run_on_node "$ip" "mount /dev/mapper/${VG_NAME}-${LV_KUBE} $MOUNT_DIR2" || return 1
    run_on_node "$ip" "mount /dev/mapper/${VG_NAME}-${LV_OPENEBS} $MOUNT_DIR3" || return 1

    # 7. 获取UUID
    local UUID_MNT=$(run_on_node_uuid "$ip" "blkid -s UUID -o value /dev/mapper/${VG_NAME}-${LV_MNT}")
    local UUID_KUBE=$(run_on_node_uuid "$ip" "blkid -s UUID -o value /dev/mapper/${VG_NAME}-${LV_KUBE}")
    local UUID_OPENEBS=$(run_on_node_uuid "$ip" "blkid -s UUID -o value /dev/mapper/${VG_NAME}-${LV_OPENEBS}")

    # 8. 备份 fstab
    run_on_node "$ip" "cp -f /etc/fstab /etc/fstab.bak.$(date +%Y%m%d%H%M)" || return 1
    # 9. 删除已存在的挂载点记录
    run_on_node "$ip" "sed -i '\#^[^#].*$MOUNT_DIR1 #d' /etc/fstab"
    run_on_node "$ip" "sed -i '\#^[^#].*$MOUNT_DIR2 #d' /etc/fstab"
    run_on_node "$ip" "sed -i '\#^[^#].*$MOUNT_DIR3 #d' /etc/fstab"

    # 10. 写入 fstab 自动挂载
    run_on_node "$ip" "echo \"UUID=$UUID_MNT $MOUNT_DIR1 xfs defaults 0 0\" >> /etc/fstab" || return 1
    run_on_node "$ip" "echo \"UUID=$UUID_KUBE $MOUNT_DIR2 xfs defaults 0 0\" >> /etc/fstab" || return 1
    run_on_node "$ip" "echo \"UUID=$UUID_OPENEBS $MOUNT_DIR3 xfs defaults 0 0\" >> /etc/fstab" || return 1

    log "✅ $ip 上的LVM设置完成"
    return 0
}

# 检查是否为root用户
check_root() {
  if [[ $(id -u) -ne 0 ]]; then
    log_error "请使用root权限运行此脚本"
    exit 1
  fi
}

# 检测系统架构
detect_arch() {
  local arch
  arch=$(uname -m)
  case ${arch} in
    x86_64)
      echo "x86"
      ;;
    aarch64|arm64)
      echo "arm"
      ;;
    *)
      log_error "不支持的架构: ${arch}"
      exit 1
      ;;
  esac
}

# 安装Docker二进制文件
install_binaries() {
  local arch=$1
  local bin_dir="${SCRIPT_DIR}/${arch}"
  
  log_info "开始安装Docker二进制文件..."
  
  # 检查二进制文件目录是否存在
  if [[ ! -d "${bin_dir}" ]]; then
    log_error "二进制文件目录不存在: ${bin_dir}"
    return 1
  fi
  
  # 复制所有二进制文件到目标目录
  for binary in docker dockerd docker-init docker-proxy containerd containerd-shim-runc-v2 ctr runc docker-compose; do
    if [[ -f "${bin_dir}/${binary}" ]]; then
      cp "${bin_dir}/${binary}" "${DOCKER_BIN_DIR}/"
      chmod +x "${DOCKER_BIN_DIR}/${binary}"
    else
      log_error "二进制文件不存在: ${binary}"
      return 1
    fi
  done
}

# 安装Docker服务
install_service() {
  log_info "安装Docker服务..."
  
  # 创建Docker配置目录
  mkdir -p "${DOCKER_CONFIG_DIR}"
  
  # 复制daemon.json
  if [[ -f "${SCRIPT_DIR}/daemon.json" ]]; then
    cp "${SCRIPT_DIR}/daemon.json" "${DOCKER_CONFIG_DIR}/"
  else
    log_error "daemon.json不存在"
    return 1
  fi
  
  # 安装systemd服务文件
  if [[ -f "${SCRIPT_DIR}/docker.service" ]]; then
    cp "${SCRIPT_DIR}/docker.service" "${DOCKER_SERVICE_DIR}/"
  else
    log_error "docker.service不存在"
    return 1
  fi
}

# 启动Docker服务
start_docker() {
  log_info "启动Docker服务..."
  
  systemctl daemon-reload
  systemctl enable docker
  systemctl start docker
  
  # 验证Docker是否正常运行
  if ! docker info >/dev/null 2>&1; then
    log_error "Docker服务启动失败"
    return 1
  fi
  
  log_info "Docker服务已成功启动"
}

# 清理函数
cleanup() {
  local exit_code=$?
  if [[ ${exit_code} -ne 0 ]]; then
    log_error "安装过程中出现错误，退出码: ${exit_code}"
  fi
  exit ${exit_code}
}

# 主函数
main() {
  # 设置清理函数
  trap cleanup EXIT

  # 检查root权限
  check_root

  # 解析命令行参数
  parse_args "$@"

  # 读取配置文件
  read_yaml_simple "$CONFIG_FILE"
  

  # 检测系统架构
  local arch
  arch=$(detect_arch)
  log_info "检测到系统架构: ${arch}"
  
  # 安装二进制文件
  install_binaries "${arch}" || exit 1
  
  # 安装服务
  install_service || exit 1
  
  # 启动Docker
  start_docker || exit 1
  
  # 导入Docker镜像
  import_docker_images || exit 1
  
  log_info "Docker安装完成！"
}

# 执行主函数
main "$@"
